'use client';

import { useAuth } from '@/hooks';
import { useRouter } from 'next/navigation';
import Navigation from './Navigation';
import HeroSection from './HeroSection';
import FeaturesSection from './FeaturesSection';
import TestimonialsSection from './TestimonialsSection';
import PricingSection from './PricingSection';
import FAQSection from './FAQSection';
import Footer from './Footer';

export default function LandingPage() {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  const handleGetStarted = () => {
    if (isAuthenticated) {
      router.push(user?.role === 'master' ? '/master/dashboard' : '/child/dashboard');
    } else {
      router.push('/auth/register');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation isAuthenticated={isAuthenticated} user={user} />
      <HeroSection
        isAuthenticated={isAuthenticated}
        user={user}
        onGetStarted={handleGetStarted}
      />
      <div id="features">
        <FeaturesSection />
      </div>
      <TestimonialsSection />
      <div id="pricing">
        <PricingSection />
      </div>
      <div id="faq">
        <FAQSection />
      </div>
      <Footer />
    </div>
  );
}
