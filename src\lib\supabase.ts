import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Browser client for SSR
export function createSupabaseBrowserClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Types for our database
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string | null
          role: 'master' | 'child'
          created_at: string
          updated_at: string
          is_demo: boolean | null
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          role?: 'master' | 'child'
          created_at?: string
          updated_at?: string
          is_demo?: boolean | null
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          role?: 'master' | 'child'
          created_at?: string
          updated_at?: string
          is_demo?: boolean | null
        }
      }
    }
  }
}
