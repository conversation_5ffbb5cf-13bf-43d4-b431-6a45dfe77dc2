import { User as SupabaseUser } from '@supabase/supabase-js';

export type UserRole = 'master' | 'child';

export interface User {
  id: string;
  email: string;
  role: UserRole;
  name?: string;
  supabaseUser?: SupabaseUser;
  isSupabaseAuth?: boolean;
  zerodhaUserId?: string;
  zerodhaAccessToken?: string;
  zerodhaRefreshToken?: string;
  isZerodhaConnected?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  setUserRole: (role: UserRole) => void;
  logout: () => void;
  connectToZerodha: () => void;
  updateUserEmail: (newEmail: string) => void;
  inviteChild: (email: string) => Promise<void>;
  getChildUsers: () => Promise<any[]>;
  isAuthenticated: boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name?: string;
  role: UserRole;
}

export interface ZerodhaAuthData {
  accessToken: string;
  refreshToken: string;
  userId: string;
}

export interface InvitationData {
  email: string;
  masterEmail: string;
  masterId: string;
  token: string;
  expiresAt: Date;
}
