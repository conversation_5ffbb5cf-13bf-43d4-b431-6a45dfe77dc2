'use client';

import axios from 'axios';

/**
 * Zerodha API client for making authenticated requests to the Zerodha API
 */
export class ZerodhaApi {
  private apiKey: string;
  private accessToken: string;
  private baseUrl = 'https://api.kite.trade';

  /**
   * Create a new ZerodhaApi instance
   */
  constructor() {
    // Get API key from environment variable
    const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;
    if (!apiKey) {
      throw new Error('Zerodha API key not found in environment variables');
    }
    this.apiKey = apiKey;

    // Get access token from localStorage or user object
    let accessToken = localStorage.getItem('zerodha_access_token');

    // If not found in localStorage directly, check user object
    if (!accessToken) {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        if (user.zerodhaAccessToken) {
          accessToken = user.zerodhaAccessToken;
        }
      }
    }

    if (!accessToken) {
      throw new Error('Zerodha access token not found in localStorage or user object');
    }
    this.accessToken = accessToken;
  }

  /**
   * Make an authenticated request to the Zerodha API via our proxy to avoid CORS issues
   * @param endpoint API endpoint (without leading slash)
   * @param method HTTP method
   * @param data Request data (for POST/PUT requests)
   * @returns Promise with the API response
   */
  private async request(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data?: any) {
    try {
      // Use our proxy endpoint to avoid CORS issues
      const response = await axios.post('/api/zerodha/proxy', {
        endpoint,
        method,
        data,
        accessToken: this.accessToken,
      });

      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to call Zerodha API');
      }

      return response.data.data;
    } catch (error: any) {
      console.error(`Zerodha API error (${endpoint}):`, error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Get user profile
   * @returns User profile data
   */
  async getProfile() {
    const response = await this.request('user/profile');
    return response.data;
  }

  /**
   * Get user margins
   * @returns User margins data
   */
  async getMargins() {
    const response = await this.request('user/margins');
    return response.data;
  }

  /**
   * Place an order
   * @param params Order parameters
   * @returns Order response
   */
  async placeOrder(params: {
    exchange: string;
    tradingsymbol: string;
    transaction_type: 'BUY' | 'SELL';
    quantity: number;
    price?: number;
    product: 'CNC' | 'MIS' | 'NRML';
    order_type: 'MARKET' | 'LIMIT' | 'SL' | 'SL-M';
    validity: 'DAY' | 'IOC';
    disclosed_quantity?: number;
    trigger_price?: number;
    squareoff?: number;
    stoploss?: number;
    trailing_stoploss?: number;
    tag?: string;
  }) {
    return this.request('orders/regular', 'POST', params);
  }

  /**
   * Get order history
   * @returns Order history
   */
  async getOrders() {
    return this.request('orders');
  }

  /**
   * Get trades
   * @returns Trades
   */
  async getTrades() {
    return this.request('trades');
  }

  /**
   * Get holdings
   * @returns Holdings
   */
  async getHoldings() {
    return this.request('portfolio/holdings');
  }

  /**
   * Get positions
   * @returns Positions
   */
  async getPositions() {
    return this.request('portfolio/positions');
  }
}
