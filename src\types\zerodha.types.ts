// Zerodha API types
export interface ZerodhaLoginUrl {
  url: string;
}

export interface ZerodhaTokenResponse {
  access_token: string;
  refresh_token: string;
  user_id: string;
  user_name: string;
  user_shortname: string;
  email: string;
  user_type: string;
  broker: string;
}

export interface ZerodhaProfile {
  user_id: string;
  user_name: string;
  user_shortname: string;
  email: string;
  user_type: string;
  broker: string;
  exchanges: string[];
  products: string[];
  order_types: string[];
}

export interface ZerodhaOrderResponse {
  order_id: string;
  status: string;
  status_message: string;
}

export interface ZerodhaPosition {
  tradingsymbol: string;
  exchange: string;
  instrument_token: number;
  product: string;
  quantity: number;
  overnight_quantity: number;
  multiplier: number;
  average_price: number;
  close_price: number;
  last_price: number;
  value: number;
  pnl: number;
  m2m: number;
  unrealised: number;
  realised: number;
  buy_quantity: number;
  buy_price: number;
  buy_value: number;
  sell_quantity: number;
  sell_price: number;
  sell_value: number;
  day_buy_quantity: number;
  day_buy_price: number;
  day_buy_value: number;
  day_sell_quantity: number;
  day_sell_price: number;
  day_sell_value: number;
}

export interface ZerodhaHolding {
  tradingsymbol: string;
  exchange: string;
  instrument_token: number;
  isin: string;
  product: string;
  price: number;
  quantity: number;
  used_quantity: number;
  t1_quantity: number;
  realised_quantity: number;
  authorised_quantity: number;
  authorised_date: string;
  opening_quantity: number;
  collateral_quantity: number;
  collateral_type: string;
  discrepancy: boolean;
  average_price: number;
  last_price: number;
  close_price: number;
  pnl: number;
  day_change: number;
  day_change_percentage: number;
}

export interface ZerodhaOrder {
  order_id: string;
  parent_order_id: string;
  exchange_order_id: string;
  placed_by: string;
  variety: string;
  status: string;
  status_message: string;
  order_timestamp: string;
  exchange_update_timestamp: string;
  exchange_timestamp: string;
  rejected_by: string;
  product: string;
  exchange: string;
  tradingsymbol: string;
  instrument_token: number;
  order_type: string;
  transaction_type: string;
  validity: string;
  price: number;
  quantity: number;
  filled_quantity: number;
  pending_quantity: number;
  cancelled_quantity: number;
  disclosed_quantity: number;
  trigger_price: number;
  average_price: number;
  tag: string;
  guid: string;
}

export interface ZerodhaQuote {
  instrument_token: number;
  timestamp: string;
  last_price: number;
  last_quantity: number;
  last_trade_time: string;
  change: number;
  ohlc: {
    open: number;
    high: number;
    low: number;
    close: number;
  };
  volume: number;
  buy_quantity: number;
  sell_quantity: number;
  depth: {
    buy: Array<{
      price: number;
      quantity: number;
      orders: number;
    }>;
    sell: Array<{
      price: number;
      quantity: number;
      orders: number;
    }>;
  };
}
