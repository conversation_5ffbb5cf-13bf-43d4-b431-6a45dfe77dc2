export const API_ENDPOINTS = {
  // Auth endpoints
  auth: {
    login: '/api/auth/login',
    register: '/api/auth/register',
    logout: '/api/auth/logout',
    profile: '/api/auth/profile',
    childRegister: '/api/auth/child-register',
  },
  
  // Zerodha endpoints
  zerodha: {
    connect: '/api/zerodha/connect',
    callback: '/api/zerodha/callback',
    profile: '/api/zerodha/profile',
    orders: '/api/zerodha/orders',
    positions: '/api/zerodha/positions',
    holdings: '/api/zerodha/holdings',
    placeOrder: '/api/zerodha/place-order',
    quotes: '/api/zerodha/quotes',
  },
  
  // Trading endpoints
  trading: {
    placeOrder: '/api/trading/place-order',
    getOrders: '/api/trading/orders',
    getPortfolio: '/api/trading/portfolio',
    getTrades: '/api/trading/trades',
    copyTrade: '/api/trading/copy-trade',
  },
  
  // User management endpoints
  users: {
    invite: '/api/invite',
    getChildren: '/api/users/children',
    getMaster: '/api/users/master',
    updateProfile: '/api/users/profile',
  },
  
  // Notification endpoints
  notifications: {
    get: '/api/notifications',
    markRead: '/api/notifications/mark-read',
    markAllRead: '/api/notifications/mark-all-read',
  },
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

export const API_ERRORS = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  ZERODHA_ERROR: 'ZERODHA_ERROR',
  DEMO_MODE_ONLY: 'DEMO_MODE_ONLY',
} as const;
