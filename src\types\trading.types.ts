export type TransactionType = 'BUY' | 'SELL';
export type OrderType = 'MARKET' | 'LIMIT' | 'SL' | 'SL-M';
export type ProductType = 'CNC' | 'MIS' | 'NRML';
export type ExchangeType = 'NSE' | 'BSE' | 'NFO' | 'BFO' | 'CDS' | 'MCX';
export type OrderStatus = 'PENDING' | 'COMPLETE' | 'CANCELLED' | 'REJECTED';

export interface TradeParams {
  tradingsymbol: string;
  exchange: ExchangeType;
  transaction_type: TransactionType;
  order_type: OrderType;
  quantity: number;
  price?: number;
  product: ProductType;
  validity?: string;
  disclosed_quantity?: number;
  trigger_price?: number;
  squareoff?: number;
  stoploss?: number;
  trailing_stoploss?: number;
  tag?: string;
}

export interface TradeResponse {
  success: boolean;
  orderId?: string;
  message?: string;
  data?: any;
}

export interface Trade {
  id: string;
  userId: string;
  symbol: string;
  exchange: ExchangeType;
  transactionType: TransactionType;
  orderType: OrderType;
  quantity: number;
  price: number;
  executedPrice?: number;
  status: OrderStatus;
  timestamp: Date;
  zerodhaOrderId?: string;
  product: ProductType;
  tag?: string;
}

export interface MockTrade {
  id: string;
  symbol: string;
  exchange: ExchangeType;
  transactionType: TransactionType;
  quantity: number;
  price: number;
  timestamp: Date;
  status: OrderStatus;
  userId: string;
  userEmail: string;
  orderType: OrderType;
  product: ProductType;
}

export interface Portfolio {
  totalValue: number;
  totalPnL: number;
  totalPnLPercentage: number;
  holdings: Holding[];
}

export interface Holding {
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  totalValue: number;
  pnl: number;
  pnlPercentage: number;
  exchange: ExchangeType;
}

export interface TradeCopy {
  id: string;
  relationshipId: string;
  originalTradeId: string;
  copiedTradeId: string;
  copiedAt: Date;
  status: 'SUCCESS' | 'FAILED';
  errorMessage?: string;
}

export interface StockPrice {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  lastUpdated: Date;
}
