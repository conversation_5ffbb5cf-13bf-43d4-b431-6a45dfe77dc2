import { prisma } from '@/lib/prisma'
import { MasterChildRelationship, User } from '@prisma/client'

export type RelationshipWithUsers = MasterChildRelationship & {
  master: User
  child: User
}

export class RelationshipService {
  // Create master-child relationship
  static async createRelationship(masterId: string, childId: string): Promise<MasterChildRelationship> {
    return prisma.masterChildRelationship.create({
      data: {
        masterId,
        childId,
      },
    })
  }

  // Get all relationships for a master
  static async getMasterRelationships(masterId: string): Promise<RelationshipWithUsers[]> {
    return prisma.masterChildRelationship.findMany({
      where: {
        masterId,
        isActive: true,
        deletedAt: null,
      },
      include: {
        master: true,
        child: {
          include: {
            zerodhaCredentials: true,
          },
        },
      },
    })
  }

  // Get all relationships for a child
  static async getChildRelationships(childId: string): Promise<RelationshipWithUsers[]> {
    return prisma.masterChildRelationship.findMany({
      where: {
        childId,
        isActive: true,
        deletedAt: null,
      },
      include: {
        master: {
          include: {
            zerodhaCredentials: true,
          },
        },
        child: true,
      },
    })
  }

  // Get specific relationship
  static async getRelationship(masterId: string, childId: string): Promise<RelationshipWithUsers | null> {
    return prisma.masterChildRelationship.findUnique({
      where: {
        masterId_childId: {
          masterId,
          childId,
        },
      },
      include: {
        master: true,
        child: true,
      },
    })
  }

  // Check if relationship exists
  static async relationshipExists(masterId: string, childId: string): Promise<boolean> {
    const relationship = await prisma.masterChildRelationship.findUnique({
      where: {
        masterId_childId: {
          masterId,
          childId,
        },
      },
    })
    return !!relationship && relationship.isActive && !relationship.deletedAt
  }

  // Deactivate relationship (soft delete)
  static async deactivateRelationship(masterId: string, childId: string): Promise<MasterChildRelationship> {
    return prisma.masterChildRelationship.update({
      where: {
        masterId_childId: {
          masterId,
          childId,
        },
      },
      data: {
        isActive: false,
        deletedAt: new Date(),
      },
    })
  }

  // Reactivate relationship
  static async reactivateRelationship(masterId: string, childId: string): Promise<MasterChildRelationship> {
    return prisma.masterChildRelationship.update({
      where: {
        masterId_childId: {
          masterId,
          childId,
        },
      },
      data: {
        isActive: true,
        deletedAt: null,
      },
    })
  }

  // Get all children for a master (simplified)
  static async getMasterChildren(masterId: string): Promise<User[]> {
    const relationships = await prisma.masterChildRelationship.findMany({
      where: {
        masterId,
        isActive: true,
        deletedAt: null,
      },
      include: {
        child: {
          include: {
            zerodhaCredentials: true,
          },
        },
      },
    })

    return relationships.map(rel => rel.child)
  }

  // Get master for a child
  static async getChildMaster(childId: string): Promise<User | null> {
    const relationship = await prisma.masterChildRelationship.findFirst({
      where: {
        childId,
        isActive: true,
        deletedAt: null,
      },
      include: {
        master: {
          include: {
            zerodhaCredentials: true,
          },
        },
      },
    })

    return relationship?.master || null
  }

  // Get relationship statistics
  static async getRelationshipStats(masterId: string): Promise<{
    totalChildren: number
    activeChildren: number
    connectedChildren: number
  }> {
    const relationships = await prisma.masterChildRelationship.findMany({
      where: {
        masterId,
        deletedAt: null,
      },
      include: {
        child: {
          include: {
            zerodhaCredentials: true,
          },
        },
      },
    })

    const totalChildren = relationships.length
    const activeChildren = relationships.filter(rel => rel.isActive).length
    const connectedChildren = relationships.filter(
      rel => rel.isActive && rel.child.zerodhaCredentials?.isConnected
    ).length

    return {
      totalChildren,
      activeChildren,
      connectedChildren,
    }
  }
}
