'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Star, Zap, Crown } from 'lucide-react';
import { motion } from 'framer-motion';
import { GlowingBorder } from '@/components/ui/animated-border';

const plans = [
  {
    name: "Free",
    description: "Perfect for getting started with copy trading",
    price: "$0",
    period: "forever",
    icon: Star,
    badge: null,
    features: [
      "1 Master account",
      "Up to 3 Child accounts",
      "Basic trade copying",
      "Email support",
      "Standard execution speed",
      "Basic analytics"
    ],
    cta: "Get started",
    popular: false
  },
  {
    name: "Pro",
    description: "Most popular choice for serious traders",
    price: "$29",
    period: "per month",
    icon: Zap,
    badge: "Most Popular",
    features: [
      "Unlimited Master accounts",
      "Up to 25 Child accounts",
      "Real-time trade copying",
      "Priority support",
      "Advanced analytics",
      "Risk management tools",
      "API access",
      "Mobile app access"
    ],
    cta: "Try for 30 days free",
    popular: true
  },
  {
    name: "Enterprise",
    description: "For large trading operations and institutions",
    price: "$99",
    period: "per month",
    icon: Crown,
    badge: "Enterprise",
    features: [
      "Unlimited everything",
      "White-label solution",
      "Custom integrations",
      "Dedicated account manager",
      "24/7 phone support",
      "Advanced reporting",
      "Custom risk controls",
      "SLA guarantee"
    ],
    cta: "Contact sales",
    popular: false
  }
];

function PricingSection() {
  return (
    <section className="py-24 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Badge variant="outline" className="mb-4 border-2 border-transparent bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-cyan-500/10 hover:from-blue-500/20 hover:via-purple-500/20 hover:to-cyan-500/20 transition-all duration-300">
              Pricing
            </Badge>
          </motion.div>
          <motion.h2
            className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl lg:text-5xl"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Choose your{' '}
            <motion.span
              className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
              style={{
                backgroundSize: "200% 200%",
              }}
            >
              trading plan
            </motion.span>
          </motion.h2>
          <motion.p
            className="mt-4 max-w-2xl mx-auto text-xl text-muted-foreground"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            Start free and scale as you grow. All plans include our core trading features.
          </motion.p>
        </motion.div>

        {/* Pricing cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              className={plan.popular ? "md:scale-105" : ""}
            >
              <GlowingBorder
                className="h-full"
                glowColor={plan.popular ? "blue" : index % 2 === 0 ? "purple" : "cyan"}
              >
                <Card className={`relative h-full border-0 bg-background/50 backdrop-blur-sm ${
                  plan.popular ? 'shadow-xl' : ''
                }`}>
                  {plan.badge && (
                    <motion.div
                      className="absolute -top-4 left-1/2 transform -translate-x-1/2"
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.5 + index * 0.2, type: "spring" }}
                    >
                      <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 shadow-lg">
                        {plan.badge}
                      </Badge>
                    </motion.div>
                  )}

                  <CardHeader className="text-center pb-8">
                    <div className="flex justify-center mb-4">
                      <motion.div
                        className={`p-3 rounded-full bg-gradient-to-r ${
                          plan.popular
                            ? 'from-blue-500/20 to-purple-500/20'
                            : 'from-muted/50 to-muted/80'
                        }`}
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <plan.icon className={`w-8 h-8 ${
                          plan.popular ? 'text-blue-600' : 'text-muted-foreground'
                        }`} />
                      </motion.div>
                    </div>

                    <CardTitle className="text-2xl font-bold text-foreground">
                      {plan.name}
                    </CardTitle>

                    <CardDescription className="text-muted-foreground mt-2">
                      {plan.description}
                    </CardDescription>

                    <div className="mt-6">
                      <motion.span
                        className="text-4xl font-bold text-foreground"
                        animate={{ scale: plan.popular ? [1, 1.05, 1] : 1 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                      >
                        {plan.price}
                      </motion.span>
                      <span className="text-muted-foreground ml-2">
                        {plan.period}
                      </span>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <ul className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <motion.li
                          key={featureIndex}
                          className="flex items-center"
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: featureIndex * 0.1 }}
                          viewport={{ once: true }}
                        >
                          <motion.div
                            whileHover={{ scale: 1.2 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                          </motion.div>
                          <span className="text-muted-foreground">{feature}</span>
                        </motion.li>
                      ))}
                    </ul>

                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        className={`w-full py-3 ${
                          plan.popular
                            ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                            : 'bg-gradient-to-r from-muted to-muted-foreground/20 hover:from-muted-foreground/20 hover:to-muted text-foreground'
                        } transition-all duration-300`}
                        size="lg"
                      >
                        {plan.cta}
                      </Button>
                    </motion.div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>
          ))}
        </div>

        {/* Bottom note */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <p className="text-muted-foreground">
            All plans include our core features. Upgrade or downgrade at any time.
          </p>
          <p className="text-sm text-muted-foreground/70 mt-2">
            Free for verified students, teachers, and open source maintainers.{' '}
            <a href="#" className="text-blue-600 hover:underline transition-colors">Learn more</a>
          </p>
        </motion.div>
      </div>
    </section>
  );
}

export default PricingSection;