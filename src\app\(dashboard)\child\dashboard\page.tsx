'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';
import Header from '@/app/components/Header';
import Button from '@/app/components/Button';
import ZerodhaStatus from '@/app/components/ZerodhaStatus';
import { isConnectedToZerodha } from '@/app/utils/orderUtils';

export default function ChildDashboardPage() {
  const router = useRouter();
  const { user, connectToZerodha } = useAuth();

  // Redirect if not authenticated or not a child
  useEffect(() => {
    if (user && user.role !== 'child') {
      router.push('/master/dashboard');
    }
  }, [user, router]);

  // Check if Zerodha is connected using our utility function
  const isZerodhaConnected = isConnectedToZerodha();

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold mb-8">Child Dashboard</h1>

      {/* Master Connection Status */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
        <div className="px-4 py-5 sm:px-6">
          <h2 className="text-lg font-medium text-gray-900">Master Account Connection</h2>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Your connection status with the master trader
          </p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Master Email</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {user?.masterEmail || 'Not available'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Your Email</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {user?.email || 'Not available'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Zerodha User ID</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {user?.zerodhaUserId || 'Not available'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Connection Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {user?.masterId ? (
                  <div className="flex items-center">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                      ✓ Connected
                    </span>
                    <span className="text-xs text-gray-500">
                      Ready to receive trade copies
                    </span>
                  </div>
                ) : (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Not Connected
                  </span>
                )}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Zerodha Status Component */}
      <ZerodhaStatus />

      {/* Copied Trades Section */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h2 className="text-lg font-medium text-gray-900">Copied Trades</h2>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Trades copied from your Master account
          </p>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          {/* Mock data - in a real app, this would come from the backend */}
          <div className="bg-gray-50 p-4 rounded-md text-center text-gray-500">
            No trades copied yet
          </div>
        </div>
      </div>
        </div>
      </main>
    </div>
  );
}
