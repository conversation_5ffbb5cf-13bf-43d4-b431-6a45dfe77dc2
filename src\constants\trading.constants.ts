export const TRANSACTION_TYPES = {
  BUY: 'BUY',
  SELL: 'SELL',
} as const;

export const ORDER_TYPES = {
  MARKET: 'MARKET',
  LIMIT: 'LIMIT',
  SL: 'SL',
  SL_M: 'SL-M',
} as const;

export const PRODUCT_TYPES = {
  CNC: 'CNC', // Cash and Carry
  MIS: 'MIS', // Margin Intraday Squareoff
  NRML: 'NRML', // Normal
} as const;

export const EXCHANGES = {
  NSE: 'NSE',
  BSE: 'BSE',
  NFO: 'NFO',
  BFO: 'BFO',
  CDS: 'CDS',
  MCX: 'MCX',
} as const;

export const ORDER_STATUS = {
  PENDING: 'PENDING',
  COMPLETE: 'COMPLETE',
  CANCELLED: 'CANCELLED',
  REJECTED: 'REJECTED',
} as const;

export const TRADE_COPY_STATUS = {
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  PENDING: 'PENDING',
} as const;

export const MOCK_STOCKS = [
  { symbol: 'RELIANCE', name: 'Reliance Industries Ltd', exchange: 'NSE' },
  { symbol: 'TCS', name: 'Tata Consultancy Services Ltd', exchange: 'NSE' },
  { symbol: 'HDFCBANK', name: 'HDFC Bank Ltd', exchange: 'NSE' },
  { symbol: 'INFY', name: 'Infosys Ltd', exchange: 'NSE' },
  { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Ltd', exchange: 'NSE' },
  { symbol: 'ICICIBANK', name: 'ICICI Bank Ltd', exchange: 'NSE' },
  { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank Ltd', exchange: 'NSE' },
  { symbol: 'BHARTIARTL', name: 'Bharti Airtel Ltd', exchange: 'NSE' },
  { symbol: 'ITC', name: 'ITC Ltd', exchange: 'NSE' },
  { symbol: 'SBIN', name: 'State Bank of India', exchange: 'NSE' },
] as const;

export const DEMO_STOCK_PRICES = {
  RELIANCE: { price: 2450.50, change: 12.30, changePercent: 0.50 },
  TCS: { price: 3890.75, change: -25.40, changePercent: -0.65 },
  HDFCBANK: { price: 1678.90, change: 8.20, changePercent: 0.49 },
  INFY: { price: 1456.30, change: 15.60, changePercent: 1.08 },
  HINDUNILVR: { price: 2234.80, change: -18.90, changePercent: -0.84 },
  ICICIBANK: { price: 1089.45, change: 22.10, changePercent: 2.07 },
  KOTAKBANK: { price: 1756.20, change: -12.80, changePercent: -0.72 },
  BHARTIARTL: { price: 1234.60, change: 34.50, changePercent: 2.87 },
  ITC: { price: 456.75, change: 5.25, changePercent: 1.16 },
  SBIN: { price: 789.30, change: -8.70, changePercent: -1.09 },
} as const;
