import { prisma } from '@/lib/prisma'
import { UserR<PERSON>, User, ZerodhaCredentials } from '@prisma/client'
import bcrypt from 'bcryptjs'

export type UserWithCredentials = User & {
  zerodhaCredentials?: ZerodhaCredentials | null
}

export class UserService {
  // Create a new user
  static async createUser(data: {
    email: string
    name?: string
    password: string
    role: UserRole
    isDemo?: boolean
  }): Promise<UserWithCredentials> {
    const hashedPassword = await bcrypt.hash(data.password, 10)
    
    return prisma.user.create({
      data: {
        email: data.email,
        name: data.name,
        password: hashedPassword,
        role: data.role,
        isDemo: data.isDemo || false,
      },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Find user by email
  static async findByEmail(email: string): Promise<UserWithCredentials | null> {
    return prisma.user.findUnique({
      where: { email },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Find user by ID
  static async findById(id: string): Promise<UserWithCredentials | null> {
    return prisma.user.findUnique({
      where: { id },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Verify user password
  static async verifyPassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password)
  }

  // Update user
  static async updateUser(id: string, data: Partial<User>): Promise<UserWithCredentials> {
    return prisma.user.update({
      where: { id },
      data,
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Soft delete user
  static async deleteUser(id: string): Promise<UserWithCredentials> {
    return prisma.user.update({
      where: { id },
      data: {
        deletedAt: new Date(),
        isActive: false,
      },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Get all active users
  static async getActiveUsers(): Promise<UserWithCredentials[]> {
    return prisma.user.findMany({
      where: {
        isActive: true,
        deletedAt: null,
      },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Get users by role
  static async getUsersByRole(role: UserRole): Promise<UserWithCredentials[]> {
    return prisma.user.findMany({
      where: {
        role,
        isActive: true,
        deletedAt: null,
      },
      include: {
        zerodhaCredentials: true,
      },
    })
  }

  // Update or create Zerodha credentials
  static async updateZerodhaCredentials(userId: string, data: {
    zerodhaUserId?: string
    apiKey?: string
    apiSecret?: string
    accessToken?: string
    refreshToken?: string
    tokenExpiry?: Date
    isConnected?: boolean
  }): Promise<ZerodhaCredentials> {
    return prisma.zerodhaCredentials.upsert({
      where: { userId },
      update: {
        ...data,
        lastSyncAt: new Date(),
      },
      create: {
        userId,
        ...data,
        lastSyncAt: new Date(),
      },
    })
  }

  // Get Zerodha credentials
  static async getZerodhaCredentials(userId: string): Promise<ZerodhaCredentials | null> {
    return prisma.zerodhaCredentials.findUnique({
      where: { userId },
    })
  }

  // Disconnect Zerodha
  static async disconnectZerodha(userId: string): Promise<ZerodhaCredentials> {
    return prisma.zerodhaCredentials.update({
      where: { userId },
      data: {
        accessToken: null,
        refreshToken: null,
        tokenExpiry: null,
        isConnected: false,
        lastSyncAt: new Date(),
      },
    })
  }
}
