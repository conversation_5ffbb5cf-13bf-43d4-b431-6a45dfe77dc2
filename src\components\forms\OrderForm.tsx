'use client';

import React, { useState } from 'react';
import { placeOrder } from '../utils/orderUtils';
import Button from './Button';
import Input from './Input';

export default function OrderForm() {
  const [formData, setFormData] = useState({
    exchange: 'NSE',
    tradingsymbol: 'INFY',
    transaction_type: 'BUY',
    quantity: 1,
    price: '',
    product: 'CNC',
    order_type: 'MARKET',
    validity: 'DAY',
  });

  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Convert numeric fields
      const orderParams = {
        ...formData,
        quantity: Number(formData.quantity),
        price: formData.price ? Number(formData.price) : undefined,
        transaction_type: formData.transaction_type as 'BUY' | 'SELL',
        product: formData.product as 'CNC' | 'MIS' | 'NRML',
        order_type: formData.order_type as 'MARKET' | 'LIMIT' | 'SL' | 'SL-M',
        validity: formData.validity as 'DAY' | 'IOC',
      };

      const response = await placeOrder(orderParams);
      setResult(response);
    } catch (err: any) {
      console.error('Order error:', err);
      setError(err.message || 'Failed to place order');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <h2 className="text-xl font-semibold mb-4">Place Order (Test)</h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Exchange</label>
            <select
              name="exchange"
              value={formData.exchange}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="NSE">NSE</option>
              <option value="BSE">BSE</option>
              <option value="NFO">NFO</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Symbol</label>
            <Input
              type="text"
              name="tradingsymbol"
              value={formData.tradingsymbol}
              onChange={handleChange}
              placeholder="e.g., INFY"
              required
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Transaction Type</label>
            <select
              name="transaction_type"
              value={formData.transaction_type}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="BUY">BUY</option>
              <option value="SELL">SELL</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
            <Input
              type="number"
              name="quantity"
              value={formData.quantity}
              onChange={handleChange}
              min="1"
              required
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Product</label>
            <select
              name="product"
              value={formData.product}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="CNC">CNC (Cash and Carry)</option>
              <option value="MIS">MIS (Intraday)</option>
              <option value="NRML">NRML (Normal)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Order Type</label>
            <select
              name="order_type"
              value={formData.order_type}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="MARKET">MARKET</option>
              <option value="LIMIT">LIMIT</option>
              <option value="SL">STOP LOSS</option>
              <option value="SL-M">STOP LOSS MARKET</option>
            </select>
          </div>

          {formData.order_type === 'LIMIT' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
              <Input
                type="number"
                name="price"
                value={formData.price}
                onChange={handleChange}
                step="0.05"
                required
                fullWidth
              />
            </div>
          )}
        </div>

        <div className="pt-4">
          <Button type="submit" isLoading={loading} fullWidth>
            Place Order
          </Button>
        </div>
      </form>

      {error && (
        <div className="mt-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded text-sm">
          <p className="font-bold">Error:</p>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className="mt-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 px-4 py-3 rounded text-sm">
          <p className="font-bold">Order Placed Successfully:</p>
          <pre className="mt-2 bg-background border border-border p-2 rounded overflow-auto text-xs text-foreground">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
