import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// JWT secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Mock database for users (in a real app, you would use a database)
// This should be the same array used in the main auth route
const users: any[] = [];

// Mock database for master-child relationships
const masterChildRelationships: Array<{
  masterId: string;
  childId: string;
  masterEmail: string;
  childEmail: string;
  zerodhaUserId: string;
  connectedAt: Date;
}> = [];

// Child user registration handler
export async function POST(request: NextRequest) {
  try {
    const { childUser, invitationData } = await request.json();

    // Validate input
    if (!childUser || !invitationData) {
      return NextResponse.json(
        { error: 'Child user data and invitation data are required' },
        { status: 400 }
      );
    }

    // Verify invitation token
    try {
      const decoded = jwt.verify(invitationData.invitation_token || '', JWT_SECRET);
      console.log('Verified invitation token:', decoded);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation token' },
        { status: 401 }
      );
    }

    // Check if child user already exists by email
    const existingUser = users.find((u) => u.email === childUser.email);
    if (existingUser) {
      // Update existing user with new Zerodha data
      existingUser.zerodhaAccessToken = childUser.zerodhaAccessToken;
      existingUser.zerodhaUserId = childUser.zerodhaUserId;
      existingUser.masterId = childUser.masterId;
      existingUser.role = 'child';
      
      console.log('Updated existing child user:', existingUser.email);
    } else {
      // Create new child user
      const newChildUser = {
        id: childUser.id,
        email: childUser.email,
        role: 'child',
        name: childUser.name,
        zerodhaAccessToken: childUser.zerodhaAccessToken,
        zerodhaUserId: childUser.zerodhaUserId,
        masterId: childUser.masterId,
        createdAt: new Date(),
      };

      // Add child user to mock database
      users.push(newChildUser);
      console.log('Created new child user:', newChildUser.email);
    }

    // Create or update master-child relationship
    const existingRelationship = masterChildRelationships.find(
      (rel) => rel.masterId === invitationData.masterId && rel.childEmail === childUser.email
    );

    if (existingRelationship) {
      // Update existing relationship
      existingRelationship.childId = childUser.id;
      existingRelationship.zerodhaUserId = childUser.zerodhaUserId;
      existingRelationship.connectedAt = new Date();
    } else {
      // Create new relationship
      masterChildRelationships.push({
        masterId: invitationData.masterId,
        childId: childUser.id,
        masterEmail: invitationData.masterEmail,
        childEmail: childUser.email,
        zerodhaUserId: childUser.zerodhaUserId,
        connectedAt: new Date(),
      });
    }

    console.log('Master-child relationships:', masterChildRelationships);

    // Create JWT token for the child user
    const token = jwt.sign(
      {
        id: childUser.id,
        email: childUser.email,
        role: 'child',
        masterId: childUser.masterId,
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    return NextResponse.json({
      success: true,
      message: 'Child user registered successfully',
      user: {
        id: childUser.id,
        email: childUser.email,
        role: 'child',
        name: childUser.name,
        masterId: childUser.masterId,
      },
      token,
    });

  } catch (error) {
    console.error('Child registration error:', error);
    return NextResponse.json(
      { error: 'Failed to register child user', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Get master-child relationships
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const masterId = searchParams.get('masterId');

    if (masterId) {
      // Return relationships for a specific master
      const masterRelationships = masterChildRelationships.filter(
        (rel) => rel.masterId === masterId
      );
      
      return NextResponse.json({
        success: true,
        relationships: masterRelationships,
      });
    } else {
      // Return all relationships (for debugging)
      return NextResponse.json({
        success: true,
        relationships: masterChildRelationships,
      });
    }
  } catch (error) {
    console.error('Error fetching relationships:', error);
    return NextResponse.json(
      { error: 'Failed to fetch relationships' },
      { status: 500 }
    );
  }
}
