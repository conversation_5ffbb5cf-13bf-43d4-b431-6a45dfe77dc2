import { prisma } from '@/lib/prisma'
import { AuditLog, AuditAction } from '@prisma/client'

export interface CreateAuditLogData {
  userId?: string
  action: AuditAction
  description: string
  metadata?: any
  ipAddress?: string
  userAgent?: string
}

export class AuditService {
  // Create a new audit log entry
  static async createLog(data: CreateAuditLogData): Promise<AuditLog> {
    return prisma.auditLog.create({
      data: {
        userId: data.userId || null,
        action: data.action,
        description: data.description,
        metadata: data.metadata || null,
        ipAddress: data.ipAddress || null,
        userAgent: data.userAgent || null,
      },
    })
  }

  // Get audit logs with optional filters
  static async getLogs(filters: {
    userId?: string
    action?: AuditAction
    startDate?: Date
    endDate?: Date
    limit?: number
    offset?: number
  } = {}): Promise<AuditLog[]> {
    const where: any = {}

    if (filters.userId) where.userId = filters.userId
    if (filters.action) where.action = filters.action
    if (filters.startDate || filters.endDate) {
      where.createdAt = {}
      if (filters.startDate) where.createdAt.gte = filters.startDate
      if (filters.endDate) where.createdAt.lte = filters.endDate
    }

    return prisma.auditLog.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: filters.limit,
      skip: filters.offset,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
          },
        },
      },
    })
  }

  // Get audit log count
  static async getLogCount(filters: {
    userId?: string
    action?: AuditAction
    startDate?: Date
    endDate?: Date
  } = {}): Promise<number> {
    const where: any = {}

    if (filters.userId) where.userId = filters.userId
    if (filters.action) where.action = filters.action
    if (filters.startDate || filters.endDate) {
      where.createdAt = {}
      if (filters.startDate) where.createdAt.gte = filters.startDate
      if (filters.endDate) where.createdAt.lte = filters.endDate
    }

    return prisma.auditLog.count({ where })
  }

  // Audit log helpers for common actions
  static async logUserCreated(
    userId: string,
    userEmail: string,
    userRole: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId,
      action: AuditAction.USER_CREATED,
      description: `User account created: ${userEmail} (${userRole})`,
      metadata: { userEmail, userRole },
      ipAddress,
      userAgent,
    })
  }

  static async logUserUpdated(
    userId: string,
    updatedFields: string[],
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId,
      action: AuditAction.USER_UPDATED,
      description: `User profile updated: ${updatedFields.join(', ')}`,
      metadata: { updatedFields },
      ipAddress,
      userAgent,
    })
  }

  static async logLogin(
    userId: string,
    userEmail: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId,
      action: AuditAction.LOGIN,
      description: `User logged in: ${userEmail}`,
      metadata: { userEmail },
      ipAddress,
      userAgent,
    })
  }

  static async logLogout(
    userId: string,
    userEmail: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId,
      action: AuditAction.LOGOUT,
      description: `User logged out: ${userEmail}`,
      metadata: { userEmail },
      ipAddress,
      userAgent,
    })
  }

  static async logTradePlaced(
    userId: string,
    tradeDetails: {
      symbol: string
      transactionType: string
      quantity: number
      price: number
      orderType: string
    },
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId,
      action: AuditAction.TRADE_PLACED,
      description: `Trade placed: ${tradeDetails.transactionType} ${tradeDetails.quantity} ${tradeDetails.symbol} at ₹${tradeDetails.price}`,
      metadata: tradeDetails,
      ipAddress,
      userAgent,
    })
  }

  static async logTradeCopied(
    masterUserId: string,
    childUserId: string,
    tradeDetails: {
      symbol: string
      transactionType: string
      quantity: number
      price: number
    },
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId: masterUserId,
      action: AuditAction.TRADE_COPIED,
      description: `Trade copied to child user: ${tradeDetails.transactionType} ${tradeDetails.quantity} ${tradeDetails.symbol} at ₹${tradeDetails.price}`,
      metadata: { ...tradeDetails, childUserId },
      ipAddress,
      userAgent,
    })
  }

  static async logInvitationSent(
    senderId: string,
    receiverEmail: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId: senderId,
      action: AuditAction.INVITATION_SENT,
      description: `Invitation sent to: ${receiverEmail}`,
      metadata: { receiverEmail },
      ipAddress,
      userAgent,
    })
  }

  static async logInvitationAccepted(
    masterId: string,
    childId: string,
    childEmail: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId: masterId,
      action: AuditAction.INVITATION_ACCEPTED,
      description: `Invitation accepted by: ${childEmail}`,
      metadata: { childId, childEmail },
      ipAddress,
      userAgent,
    })
  }

  static async logZerodhaConnected(
    userId: string,
    zerodhaUserId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId,
      action: AuditAction.ZERODHA_CONNECTED,
      description: `Zerodha account connected: ${zerodhaUserId}`,
      metadata: { zerodhaUserId },
      ipAddress,
      userAgent,
    })
  }

  static async logZerodhaDisconnected(
    userId: string,
    zerodhaUserId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuditLog> {
    return this.createLog({
      userId,
      action: AuditAction.ZERODHA_DISCONNECTED,
      description: `Zerodha account disconnected: ${zerodhaUserId}`,
      metadata: { zerodhaUserId },
      ipAddress,
      userAgent,
    })
  }

  // Delete old audit logs (for compliance/storage management)
  static async deleteOldLogs(daysOld: number = 365): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)

    const result = await prisma.auditLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
      },
    })

    return result.count
  }

  // Get audit statistics
  static async getAuditStats(filters: {
    userId?: string
    startDate?: Date
    endDate?: Date
  } = {}): Promise<{
    totalLogs: number
    actionBreakdown: Record<AuditAction, number>
    dailyActivity: Array<{ date: string; count: number }>
  }> {
    const where: any = {}
    if (filters.userId) where.userId = filters.userId
    if (filters.startDate || filters.endDate) {
      where.createdAt = {}
      if (filters.startDate) where.createdAt.gte = filters.startDate
      if (filters.endDate) where.createdAt.lte = filters.endDate
    }

    const logs = await prisma.auditLog.findMany({ where })

    const totalLogs = logs.length

    const actionBreakdown = logs.reduce((acc, log) => {
      acc[log.action] = (acc[log.action] || 0) + 1
      return acc
    }, {} as Record<AuditAction, number>)

    // Group by date for daily activity
    const dailyActivity = logs.reduce((acc, log) => {
      const date = log.createdAt.toISOString().split('T')[0]
      const existing = acc.find(item => item.date === date)
      if (existing) {
        existing.count++
      } else {
        acc.push({ date, count: 1 })
      }
      return acc
    }, [] as Array<{ date: string; count: number }>)

    return { totalLogs, actionBreakdown, dailyActivity }
  }
}
