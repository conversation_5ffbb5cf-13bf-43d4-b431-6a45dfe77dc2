import { prisma } from '@/lib/prisma'
import { Portfolio } from '@prisma/client'
import { Decimal } from '@prisma/client/runtime/library'

export interface CreatePortfolioData {
  userId: string
  symbol: string
  exchange: string
  quantity: number
  averagePrice: number
  currentPrice: number
  isDemo?: boolean
}

export interface UpdatePortfolioData {
  quantity?: number
  averagePrice?: number
  currentPrice?: number
  dayChange?: number
  dayChangePercent?: number
}

export interface PortfolioSummary {
  totalValue: number
  totalPnL: number
  totalDayChange: number
  totalDayChangePercent: number
  holdings: Portfolio[]
}

export class PortfolioService {
  // Create or update portfolio holding
  static async upsertHolding(data: CreatePortfolioData): Promise<Portfolio> {
    const pnl = (data.currentPrice - data.averagePrice) * data.quantity
    const dayChange = data.currentPrice * 0.01 // Simplified day change calculation
    const dayChangePercent = (dayChange / data.currentPrice) * 100

    return prisma.portfolio.upsert({
      where: {
        userId_symbol_exchange: {
          userId: data.userId,
          symbol: data.symbol,
          exchange: data.exchange,
        },
      },
      update: {
        quantity: data.quantity,
        averagePrice: new Decimal(data.averagePrice),
        currentPrice: new Decimal(data.currentPrice),
        pnl: new Decimal(pnl),
        dayChange: new Decimal(dayChange),
        dayChangePercent: new Decimal(dayChangePercent),
        lastUpdated: new Date(),
      },
      create: {
        userId: data.userId,
        symbol: data.symbol,
        exchange: data.exchange,
        quantity: data.quantity,
        averagePrice: new Decimal(data.averagePrice),
        currentPrice: new Decimal(data.currentPrice),
        pnl: new Decimal(pnl),
        dayChange: new Decimal(dayChange),
        dayChangePercent: new Decimal(dayChangePercent),
        isDemo: data.isDemo || false,
      },
    })
  }

  // Get user portfolio
  static async getUserPortfolio(userId: string): Promise<Portfolio[]> {
    return prisma.portfolio.findMany({
      where: { userId },
      orderBy: { symbol: 'asc' },
    })
  }

  // Get portfolio summary
  static async getPortfolioSummary(userId: string): Promise<PortfolioSummary> {
    const holdings = await this.getUserPortfolio(userId)

    const totalValue = holdings.reduce(
      (sum, holding) => sum + (holding.currentPrice.toNumber() * holding.quantity),
      0
    )

    const totalPnL = holdings.reduce(
      (sum, holding) => sum + holding.pnl.toNumber(),
      0
    )

    const totalDayChange = holdings.reduce(
      (sum, holding) => sum + holding.dayChange.toNumber(),
      0
    )

    const totalDayChangePercent = totalValue > 0 ? (totalDayChange / totalValue) * 100 : 0

    return {
      totalValue,
      totalPnL,
      totalDayChange,
      totalDayChangePercent,
      holdings,
    }
  }

  // Update holding prices
  static async updateHoldingPrices(
    userId: string,
    symbol: string,
    exchange: string,
    currentPrice: number
  ): Promise<Portfolio> {
    const holding = await prisma.portfolio.findUnique({
      where: {
        userId_symbol_exchange: {
          userId,
          symbol,
          exchange,
        },
      },
    })

    if (!holding) {
      throw new Error('Holding not found')
    }

    const pnl = (currentPrice - holding.averagePrice.toNumber()) * holding.quantity
    const dayChange = currentPrice - holding.currentPrice.toNumber()
    const dayChangePercent = holding.currentPrice.toNumber() > 0 
      ? (dayChange / holding.currentPrice.toNumber()) * 100 
      : 0

    return prisma.portfolio.update({
      where: {
        userId_symbol_exchange: {
          userId,
          symbol,
          exchange,
        },
      },
      data: {
        currentPrice: new Decimal(currentPrice),
        pnl: new Decimal(pnl),
        dayChange: new Decimal(dayChange),
        dayChangePercent: new Decimal(dayChangePercent),
        lastUpdated: new Date(),
      },
    })
  }

  // Update holding quantity (after trade execution)
  static async updateHoldingQuantity(
    userId: string,
    symbol: string,
    exchange: string,
    quantityChange: number,
    tradePrice: number
  ): Promise<Portfolio> {
    const holding = await prisma.portfolio.findUnique({
      where: {
        userId_symbol_exchange: {
          userId,
          symbol,
          exchange,
        },
      },
    })

    if (!holding) {
      // Create new holding if it doesn't exist
      return this.upsertHolding({
        userId,
        symbol,
        exchange,
        quantity: quantityChange,
        averagePrice: tradePrice,
        currentPrice: tradePrice,
      })
    }

    const newQuantity = holding.quantity + quantityChange

    if (newQuantity <= 0) {
      // Remove holding if quantity becomes zero or negative
      await prisma.portfolio.delete({
        where: {
          userId_symbol_exchange: {
            userId,
            symbol,
            exchange,
          },
        },
      })
      
      // Return a dummy portfolio object for consistency
      return {
        ...holding,
        quantity: 0,
      }
    }

    // Calculate new average price
    const currentValue = holding.averagePrice.toNumber() * holding.quantity
    const tradeValue = tradePrice * quantityChange
    const newAveragePrice = (currentValue + tradeValue) / newQuantity

    const pnl = (holding.currentPrice.toNumber() - newAveragePrice) * newQuantity

    return prisma.portfolio.update({
      where: {
        userId_symbol_exchange: {
          userId,
          symbol,
          exchange,
        },
      },
      data: {
        quantity: newQuantity,
        averagePrice: new Decimal(newAveragePrice),
        pnl: new Decimal(pnl),
        lastUpdated: new Date(),
      },
    })
  }

  // Get holding by symbol
  static async getHolding(
    userId: string,
    symbol: string,
    exchange: string
  ): Promise<Portfolio | null> {
    return prisma.portfolio.findUnique({
      where: {
        userId_symbol_exchange: {
          userId,
          symbol,
          exchange,
        },
      },
    })
  }

  // Delete holding
  static async deleteHolding(
    userId: string,
    symbol: string,
    exchange: string
  ): Promise<void> {
    await prisma.portfolio.delete({
      where: {
        userId_symbol_exchange: {
          userId,
          symbol,
          exchange,
        },
      },
    })
  }

  // Bulk update prices for multiple holdings
  static async bulkUpdatePrices(
    priceUpdates: Array<{
      symbol: string
      exchange: string
      currentPrice: number
    }>
  ): Promise<void> {
    const updatePromises = priceUpdates.map(async (update) => {
      const holdings = await prisma.portfolio.findMany({
        where: {
          symbol: update.symbol,
          exchange: update.exchange,
        },
      })

      return Promise.all(
        holdings.map((holding) =>
          this.updateHoldingPrices(
            holding.userId,
            holding.symbol,
            holding.exchange,
            update.currentPrice
          )
        )
      )
    })

    await Promise.all(updatePromises)
  }
}
