export interface MasterChildRelationship {
  id: string;
  masterId: string;
  childId: string;
  masterEmail: string;
  childEmail: string;
  zerodhaUserId?: string;
  connectedAt: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  role: 'master' | 'child';
  zerodhaUserId?: string;
  isZerodhaConnected: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChildUser {
  id: string;
  email: string;
  name?: string;
  zerodhaUserId?: string;
  connectedAt: Date;
  isActive: boolean;
}

export interface MasterUser {
  id: string;
  email: string;
  name?: string;
  zerodhaUserId?: string;
  childUsers: ChildUser[];
  totalChildUsers: number;
}
