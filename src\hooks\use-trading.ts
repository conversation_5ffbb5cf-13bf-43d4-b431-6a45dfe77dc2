import { useState, useEffect } from 'react';
import { enhancedTradingService } from '@/utils/enhancedTradingService';
import type { Portfolio, Trade, TradeParams, TradeResponse } from '@/types';

export const useTrading = () => {
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [orders, setOrders] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadPortfolio = async () => {
    try {
      setLoading(true);
      setError(null);
      const portfolioData = await enhancedTradingService.getPortfolio();
      setPortfolio(portfolioData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load portfolio');
    } finally {
      setLoading(false);
    }
  };

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      const ordersData = await enhancedTradingService.getOrders();
      setOrders(ordersData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const placeOrder = async (params: TradeParams): Promise<TradeResponse> => {
    try {
      setLoading(true);
      setError(null);
      const response = await enhancedTradingService.placeOrder(params);
      
      // Refresh data after placing order
      await Promise.all([loadPortfolio(), loadOrders()]);
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to place order';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    await Promise.all([loadPortfolio(), loadOrders()]);
  };

  useEffect(() => {
    refreshData();
  }, []);

  return {
    portfolio,
    orders,
    loading,
    error,
    placeOrder,
    refreshData,
    loadPortfolio,
    loadOrders,
  };
};
