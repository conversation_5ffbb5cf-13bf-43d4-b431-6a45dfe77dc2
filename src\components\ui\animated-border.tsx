"use client"

import { motion } from "framer-motion"
import { ReactNode } from "react"

interface AnimatedBorderProps {
  children: ReactNode
  className?: string
  borderClassName?: string
  duration?: number
  delay?: number
}

export function AnimatedBorder({
  children,
  className = "",
  borderClassName = "",
  duration = 3,
  delay = 0
}: AnimatedBorderProps) {
  return (
    <motion.div
      className={`relative overflow-hidden rounded-lg ${className}`}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.02 }}
    >
      {/* Animated border */}
      <motion.div
        className={`absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 ${borderClassName}`}
        animate={{
          background: [
            "linear-gradient(0deg, #3b82f6, #8b5cf6, #06b6d4)",
            "linear-gradient(90deg, #8b5cf6, #06b6d4, #3b82f6)",
            "linear-gradient(180deg, #06b6d4, #3b82f6, #8b5cf6)",
            "linear-gradient(270deg, #3b82f6, #8b5cf6, #06b6d4)",
            "linear-gradient(360deg, #8b5cf6, #06b6d4, #3b82f6)"
          ]
        }}
        transition={{
          duration,
          repeat: Infinity,
          ease: "linear"
        }}
        style={{
          padding: "2px"
        }}
      >
        {/* Content container */}
        <div className="h-full w-full rounded-lg bg-background">
          {children}
        </div>
      </motion.div>
    </motion.div>
  )
}

export function GlowingBorder({
  children,
  className = "",
  glowColor = "blue"
}: {
  children: ReactNode
  className?: string
  glowColor?: "blue" | "purple" | "cyan" | "pink"
}) {
  const glowColors = {
    blue: "shadow-blue-500/25 dark:shadow-blue-400/20",
    purple: "shadow-purple-500/25 dark:shadow-purple-400/20",
    cyan: "shadow-cyan-500/25 dark:shadow-cyan-400/20",
    pink: "shadow-pink-500/25 dark:shadow-pink-400/20"
  }

  const gradientColors = {
    blue: "from-blue-500/20 via-blue-600/20 to-purple-600/20 dark:from-blue-400/30 dark:via-blue-500/30 dark:to-purple-500/30",
    purple: "from-purple-500/20 via-purple-600/20 to-pink-600/20 dark:from-purple-400/30 dark:via-purple-500/30 dark:to-pink-500/30",
    cyan: "from-cyan-500/20 via-cyan-600/20 to-blue-600/20 dark:from-cyan-400/30 dark:via-cyan-500/30 dark:to-blue-500/30",
    pink: "from-pink-500/20 via-pink-600/20 to-purple-600/20 dark:from-pink-400/30 dark:via-pink-500/30 dark:to-purple-500/30"
  }

  return (
    <motion.div
      className={`relative ${className}`}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        className={`absolute -inset-0.5 bg-gradient-to-r ${gradientColors[glowColor]} rounded-lg blur opacity-40 dark:opacity-60`}
        animate={{
          opacity: [0.4, 0.7, 0.4]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <div className={`relative bg-background/80 dark:bg-background/90 backdrop-blur-sm rounded-lg border border-border/50 dark:border-border/30 ${glowColors[glowColor]} shadow-lg`}>
        {children}
      </div>
    </motion.div>
  )
}
