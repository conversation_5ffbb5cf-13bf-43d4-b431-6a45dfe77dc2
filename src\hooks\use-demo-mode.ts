import { DEMO_MODE } from '@/constants';

export const useDemoMode = () => {
  const isDemoMode = () => DEMO_MODE.enabled;
  
  const getDemoData = (key: string) => {
    if (typeof window === 'undefined') return null;
    
    try {
      const data = localStorage.getItem(DEMO_MODE.storageKey);
      if (!data) return null;
      
      const parsedData = JSON.parse(data);
      return parsedData[key] || null;
    } catch (error) {
      console.error('Error getting demo data:', error);
      return null;
    }
  };
  
  const setDemoData = (key: string, value: any) => {
    if (typeof window === 'undefined') return;
    
    try {
      const existingData = localStorage.getItem(DEMO_MODE.storageKey);
      const data = existingData ? JSON.parse(existingData) : {};
      
      data[key] = value;
      localStorage.setItem(DEMO_MODE.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('Error setting demo data:', error);
    }
  };
  
  const clearDemoData = () => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(DEMO_MODE.storageKey);
  };
  
  return {
    isDemoMode,
    getDemoData,
    setDemoData,
    clearDemoData,
  };
};
