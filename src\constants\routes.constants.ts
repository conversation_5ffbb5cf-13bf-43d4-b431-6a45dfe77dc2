export const ROUTES = {
  // Public routes
  home: '/',
  
  // Auth routes
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    callback: '/auth/callback',
    acceptInvitation: '/auth/accept-invitation',
    zerodha: {
      connect: '/auth/zerodha',
      callback: '/auth/zerodha/callback',
      childCallback: '/auth/zerodha/child-callback',
    },
  },
  
  // Dashboard routes
  dashboard: {
    master: '/master/dashboard',
    child: '/child/dashboard',
  },
  
  // Master routes
  master: {
    dashboard: '/master/dashboard',
    configureZerodha: '/master/configure-zerodha',
    connectZerodha: '/master/connect-zerodha',
  },
  
  // Child routes
  child: {
    dashboard: '/child/dashboard',
    configureZerodha: '/child/configure-zerodha',
    connectZerodha: '/child/connect-zerodha',
  },
  
  // Demo routes
  demo: {
    trading: '/demo/trading',
  },
  
  // API routes
  api: {
    auth: '/api/auth',
    zerodha: '/api/zerodha',
    trading: '/api/trading',
    invite: '/api/invite',
    users: '/api/users',
    notifications: '/api/notifications',
  },
} as const;

export const PUBLIC_ROUTES = [
  ROUTES.home,
  ROUTES.auth.login,
  ROUTES.auth.register,
  ROUTES.auth.callback,
  ROUTES.auth.acceptInvitation,
] as const;

export const PROTECTED_ROUTES = [
  ROUTES.dashboard.master,
  ROUTES.dashboard.child,
  ROUTES.master.dashboard,
  ROUTES.master.configureZerodha,
  ROUTES.master.connectZerodha,
  ROUTES.child.dashboard,
  ROUTES.child.configureZerodha,
  ROUTES.child.connectZerodha,
  ROUTES.demo.trading,
] as const;
