export const APP_CONFIG = {
  name: 'CopyTrade',
  description: 'AI-powered trading platform for copying trades',
  version: '1.0.0',
  author: 'CopyTrade Team',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
} as const;

export const DEMO_MODE = {
  enabled: process.env.NEXT_PUBLIC_DEMO_MODE === 'true',
  storageKey: 'copyTrade_demoData',
  mockDataRefreshInterval: 5000, // 5 seconds
} as const;

export const LOCAL_STORAGE_KEYS = {
  user: 'copyTrade_user',
  zerodhaTokens: 'copyTrade_zerodhaTokens',
  demoData: 'copyTrade_demoData',
  theme: 'copyTrade_theme',
} as const;

export const SESSION_STORAGE_KEYS = {
  redirectUrl: 'copyTrade_redirectUrl',
  tempUserData: 'copyTrade_tempUserData',
} as const;

export const USER_ROLES = {
  master: 'master',
  child: 'child',
} as const;

export const ZERODHA_CONFIG = {
  apiKey: process.env.NEXT_PUBLIC_ZERODHA_API_KEY || '',
  redirectUrl: process.env.NEXT_PUBLIC_ZERODHA_REDIRECT_URL || '',
  loginUrl: 'https://kite.zerodha.com/connect/login',
  baseUrl: 'https://api.kite.trade',
} as const;

export const EMAIL_CONFIG = {
  from: process.env.EMAIL_FROM || '<EMAIL>',
  replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>',
} as const;

export const PAGINATION = {
  defaultLimit: 20,
  maxLimit: 100,
} as const;
