// Prisma model types
export interface DbUser {
  id: string;
  email: string;
  name?: string;
  role: 'master' | 'child';
  zerodhaUserId?: string;
  zerodhaAccessToken?: string;
  zerodhaRefreshToken?: string;
  isZerodhaConnected: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DbMasterChildRelationship {
  id: string;
  masterId: string;
  childId: string;
  masterEmail: string;
  childEmail: string;
  zerodhaUserId?: string;
  connectedAt: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DbTrade {
  id: string;
  userId: string;
  symbol: string;
  exchange: string;
  transactionType: string;
  orderType: string;
  quantity: number;
  price: number;
  executedPrice?: number;
  status: string;
  timestamp: Date;
  zerodhaOrderId?: string;
  product: string;
  tag?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface DbTradeCopy {
  id: string;
  relationshipId: string;
  originalTradeId: string;
  copiedTradeId: string;
  copiedAt: Date;
  status: string;
  errorMessage?: string;
}

export interface DbInvitation {
  id: string;
  email: string;
  masterEmail: string;
  masterId: string;
  token: string;
  isAccepted: boolean;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface DbAuditLog {
  id: string;
  userId: string;
  action: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

export interface DbNotification {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  isRead: boolean;
  data?: any;
  createdAt: Date;
  updatedAt: Date;
}
