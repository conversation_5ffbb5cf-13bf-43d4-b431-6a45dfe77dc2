// Enhanced Trading Service with Demo Mode Support
import { isDemoMode } from '@/constants/demoMode';
import { mockTradingService, MockTrade, MockPortfolio } from './mockTradingService';
import { authService } from './authService';

export interface TradeParams {
  exchange: string;
  tradingsymbol: string;
  transaction_type: 'BUY' | 'SELL';
  quantity: number;
  price?: number;
  product: 'CNC' | 'MIS' | 'NRML';
  order_type: 'MARKET' | 'LIMIT' | 'SL' | 'SL-M';
  validity: 'DAY' | 'IOC';
  disclosed_quantity?: number;
  trigger_price?: number;
  squareoff?: number;
  stoploss?: number;
  trailing_stoploss?: number;
  tag?: string;
}

export interface TradeResponse {
  order_id: string;
  status: string;
  message?: string;
}

export interface Portfolio {
  userId: string;
  holdings: {
    symbol: string;
    quantity: number;
    averagePrice: number;
    currentPrice: number;
    pnl: number;
  }[];
  totalValue: number;
  totalPnl: number;
}

class EnhancedTradingService {

  // Place an order (real or mock based on demo mode)
  async placeOrder(params: TradeParams): Promise<TradeResponse> {
    const user = authService.getCurrentUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    if (isDemoMode()) {
      // Use mock trading service
      const result = await mockTradingService.placeOrder({
        ...params,
        userId: user.id,
        userEmail: user.email
      });

      // If this is a master user's trade, copy to children
      if (user.role === 'master') {
        await this.copyTradeToChildren(user.id, params);
      }

      return result;
    } else {
      // Use real Zerodha API
      return this.placeRealOrder(params);
    }
  }

  // Place real order via Zerodha API
  private async placeRealOrder(params: TradeParams): Promise<TradeResponse> {
    try {
      const user = authService.getCurrentUser();

      if (!user?.zerodhaAccessToken) {
        throw new Error('No access token found. Please connect to Zerodha first.');
      }

      // Use proxy endpoint to avoid CORS issues
      const response = await fetch('/api/zerodha/proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: 'orders/regular',
          method: 'POST',
          data: params,
          accessToken: user.zerodhaAccessToken,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to place order');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to place order');
      }

      return {
        order_id: data.data.order_id,
        status: data.data.status || 'COMPLETE',
        message: 'Order placed successfully'
      };
    } catch (error: any) {
      console.error('Error placing real order:', error);
      throw error;
    }
  }

  // Get order history
  async getOrders(): Promise<any[]> {
    const user = authService.getCurrentUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    if (isDemoMode()) {
      // Use mock trading service
      const mockTrades = await mockTradingService.getOrders(user.id);
      return this.convertMockTradesToOrders(mockTrades);
    } else {
      // Use real Zerodha API
      return this.getRealOrders();
    }
  }

  // Get real orders from Zerodha API
  private async getRealOrders(): Promise<any[]> {
    try {
      const user = authService.getCurrentUser();

      if (!user?.zerodhaAccessToken) {
        throw new Error('No access token found. Please connect to Zerodha first.');
      }

      const response = await fetch('/api/zerodha/proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: 'orders',
          method: 'GET',
          accessToken: user.zerodhaAccessToken,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get orders');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to get orders');
      }

      return data.data;
    } catch (error: any) {
      console.error('Error getting real orders:', error);
      throw error;
    }
  }

  // Get portfolio/positions
  async getPortfolio(): Promise<Portfolio | null> {
    const user = authService.getCurrentUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    if (isDemoMode()) {
      // Use mock trading service
      return await mockTradingService.getPortfolio(user.id);
    } else {
      // Use real Zerodha API
      return this.getRealPortfolio();
    }
  }

  // Get real portfolio from Zerodha API
  private async getRealPortfolio(): Promise<Portfolio | null> {
    try {
      const user = authService.getCurrentUser();

      if (!user?.zerodhaAccessToken) {
        throw new Error('No access token found. Please connect to Zerodha first.');
      }

      const response = await fetch('/api/zerodha/proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: 'portfolio/positions',
          method: 'GET',
          accessToken: user.zerodhaAccessToken,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get portfolio');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to get portfolio');
      }

      // Convert Zerodha positions to our Portfolio format
      return this.convertZerodhaPositionsToPortfolio(data.data, user.id);
    } catch (error: any) {
      console.error('Error getting real portfolio:', error);
      throw error;
    }
  }

  // Copy trade to child users (for master accounts)
  private async copyTradeToChildren(masterId: string, tradeParams: TradeParams): Promise<void> {
    try {
      // Get child users for this master
      const response = await fetch(`/api/auth/child-register?masterId=${masterId}`);

      if (response.ok) {
        const data = await response.json();
        const childUsers = data.relationships || [];

        if (childUsers.length > 0 && isDemoMode()) {
          // Create a mock trade for the master
          const masterTrade: MockTrade = {
            id: `MASTER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            symbol: tradeParams.tradingsymbol,
            exchange: tradeParams.exchange,
            transactionType: tradeParams.transaction_type,
            quantity: tradeParams.quantity,
            price: tradeParams.price || 0,
            timestamp: new Date(),
            status: 'COMPLETE',
            userId: masterId,
            userEmail: authService.getCurrentUser()?.email || '',
            orderType: tradeParams.order_type,
            product: tradeParams.product
          };

          // Copy to children
          await mockTradingService.copyTradeToChildren(
            masterTrade,
            childUsers.map((child: any) => ({
              id: child.childId,
              email: child.childEmail
            }))
          );
        }
      }
    } catch (error) {
      console.error('Error copying trade to children:', error);
      // Don't throw error here as the master's trade should still succeed
    }
  }

  // Helper method to convert mock trades to order format
  private convertMockTradesToOrders(mockTrades: MockTrade[]): any[] {
    return mockTrades.map(trade => ({
      order_id: trade.id,
      tradingsymbol: trade.symbol,
      exchange: trade.exchange,
      transaction_type: trade.transactionType,
      quantity: trade.quantity,
      price: trade.price,
      order_type: trade.orderType,
      product: trade.product,
      status: trade.status,
      order_timestamp: trade.timestamp,
      filled_quantity: trade.status === 'COMPLETE' ? trade.quantity : 0,
      pending_quantity: trade.status === 'PENDING' ? trade.quantity : 0,
      cancelled_quantity: trade.status === 'REJECTED' ? trade.quantity : 0
    }));
  }

  // Helper method to convert Zerodha positions to our Portfolio format
  private convertZerodhaPositionsToPortfolio(positions: any, userId: string): Portfolio {
    const holdings = positions.net.map((position: any) => ({
      symbol: position.tradingsymbol,
      quantity: position.quantity,
      averagePrice: position.average_price,
      currentPrice: position.last_price,
      pnl: position.pnl
    }));

    const totalValue = holdings.reduce((sum: number, h: any) => sum + (h.currentPrice * h.quantity), 0);
    const totalPnl = holdings.reduce((sum: number, h: any) => sum + h.pnl, 0);

    return {
      userId,
      holdings,
      totalValue,
      totalPnl
    };
  }

  // Get current stock prices (for demo mode)
  getStockPrices(): { [symbol: string]: number } {
    if (isDemoMode()) {
      return mockTradingService.getStockPrices();
    }
    return {};
  }

  // Clear demo data (for testing)
  clearDemoData(): void {
    if (isDemoMode()) {
      mockTradingService.clearAllData();
    }
  }
}

// Export singleton instance
export const enhancedTradingService = new EnhancedTradingService();
export default enhancedTradingService;
