'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/app/components/Button';
import Input from '@/app/components/Input';
import DemoModeIndicator, { DemoModeWarning, DemoModeFeatures } from '@/app/components/DemoModeIndicator';
import { isDemoMode } from '@/app/config/demoMode';
import { enhancedTradingService } from '@/app/utils/enhancedTradingService';
import { authService } from '@/app/utils/authService';

export default function DemoTradingPage() {
  const [symbol, setSymbol] = useState('RELIANCE');
  const [quantity, setQuantity] = useState(10);
  const [price, setPrice] = useState(2450.50);
  const [transactionType, setTransactionType] = useState<'BUY' | 'SELL'>('BUY');
  const [orderType, setOrderType] = useState<'MARKET' | 'LIMIT'>('MARKET');
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);
  const [orderResult, setOrderResult] = useState<any>(null);
  const [portfolio, setPortfolio] = useState<any>(null);
  const [orders, setOrders] = useState<any[]>([]);
  const [stockPrices, setStockPrices] = useState<{ [symbol: string]: number }>({});

  const router = useRouter();

  // Redirect if not in demo mode
  useEffect(() => {
    if (!isDemoMode()) {
      router.push('/master/dashboard');
    }
  }, [router]);

  // Load initial data
  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      const [portfolioData, ordersData] = await Promise.all([
        enhancedTradingService.getPortfolio(),
        enhancedTradingService.getOrders()
      ]);
      
      setPortfolio(portfolioData);
      setOrders(ordersData);
      setStockPrices(enhancedTradingService.getStockPrices());
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const handlePlaceOrder = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsPlacingOrder(true);
    setOrderResult(null);

    try {
      const result = await enhancedTradingService.placeOrder({
        exchange: 'NSE',
        tradingsymbol: symbol,
        transaction_type: transactionType,
        quantity: quantity,
        price: orderType === 'LIMIT' ? price : undefined,
        product: 'CNC',
        order_type: orderType,
        validity: 'DAY'
      });

      setOrderResult(result);
      
      // Reload data after placing order
      setTimeout(loadData, 1000);
    } catch (error: any) {
      setOrderResult({ error: error.message });
    } finally {
      setIsPlacingOrder(false);
    }
  };

  const clearDemoData = () => {
    enhancedTradingService.clearDemoData();
    loadData();
    setOrderResult(null);
  };

  if (!isDemoMode()) {
    return null;
  }

  const user = authService.getCurrentUser();

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Demo Trading</h1>
        <Button onClick={() => router.push('/master/dashboard')} variant="outline">
          Back to Dashboard
        </Button>
      </div>

      {/* Demo Mode Indicator */}
      <DemoModeIndicator variant="banner" />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Trading Form */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Place Demo Order</h2>
            
            <DemoModeWarning action="trade" />

            <form onSubmit={handlePlaceOrder} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Stock Symbol
                </label>
                <select
                  value={symbol}
                  onChange={(e) => setSymbol(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {Object.keys(stockPrices).map(sym => (
                    <option key={sym} value={sym}>
                      {sym} - ₹{stockPrices[sym]?.toFixed(2)}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Transaction Type
                  </label>
                  <select
                    value={transactionType}
                    onChange={(e) => setTransactionType(e.target.value as 'BUY' | 'SELL')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="BUY">BUY</option>
                    <option value="SELL">SELL</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Order Type
                  </label>
                  <select
                    value={orderType}
                    onChange={(e) => setOrderType(e.target.value as 'MARKET' | 'LIMIT')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="MARKET">MARKET</option>
                    <option value="LIMIT">LIMIT</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quantity
                </label>
                <Input
                  type="number"
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value))}
                  min="1"
                  required
                />
              </div>

              {orderType === 'LIMIT' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price (₹)
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={price}
                    onChange={(e) => setPrice(parseFloat(e.target.value))}
                    required
                  />
                </div>
              )}

              <Button
                type="submit"
                isLoading={isPlacingOrder}
                className="w-full"
              >
                Place Demo Order
              </Button>
            </form>

            {orderResult && (
              <div className={`mt-4 p-4 rounded-lg ${
                orderResult.error 
                  ? 'bg-red-50 border border-red-200 text-red-800'
                  : 'bg-green-50 border border-green-200 text-green-800'
              }`}>
                {orderResult.error ? (
                  <div>
                    <strong>Error:</strong> {orderResult.error}
                  </div>
                ) : (
                  <div>
                    <strong>Order Placed Successfully!</strong>
                    <br />
                    Order ID: {orderResult.order_id}
                    <br />
                    Status: {orderResult.status}
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Demo Controls</h2>
            </div>
            
            <div className="space-y-3">
              <Button
                onClick={clearDemoData}
                variant="outline"
                className="w-full"
              >
                Clear All Demo Data
              </Button>
              
              <div className="text-sm text-gray-600">
                <p>• Stock prices update every 30 seconds</p>
                <p>• Orders have 95% success rate</p>
                <p>• Portfolio updates in real-time</p>
              </div>
            </div>
          </div>
        </div>

        {/* Portfolio and Orders */}
        <div className="space-y-6">
          {/* Portfolio */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Demo Portfolio</h2>
            
            {portfolio ? (
              <div>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="text-sm text-blue-600">Total Value</div>
                    <div className="text-lg font-semibold text-blue-900">
                      ₹{portfolio.totalValue.toFixed(2)}
                    </div>
                  </div>
                  <div className={`p-3 rounded-lg ${
                    portfolio.totalPnl >= 0 ? 'bg-green-50' : 'bg-red-50'
                  }`}>
                    <div className={`text-sm ${
                      portfolio.totalPnl >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      Total P&L
                    </div>
                    <div className={`text-lg font-semibold ${
                      portfolio.totalPnl >= 0 ? 'text-green-900' : 'text-red-900'
                    }`}>
                      ₹{portfolio.totalPnl.toFixed(2)}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-900">Holdings</h3>
                  {portfolio.holdings.length > 0 ? (
                    portfolio.holdings.map((holding: any, index: number) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-medium">{holding.symbol}</div>
                          <div className="text-sm text-gray-600">
                            {holding.quantity} @ ₹{holding.averagePrice.toFixed(2)}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">₹{holding.currentPrice.toFixed(2)}</div>
                          <div className={`text-sm ${
                            holding.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {holding.pnl >= 0 ? '+' : ''}₹{holding.pnl.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-gray-500 text-center py-4">
                      No holdings yet. Place some orders to get started!
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-center py-4">
                Loading portfolio...
              </div>
            )}
          </div>

          {/* Recent Orders */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Demo Orders</h2>
            
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {orders.length > 0 ? (
                orders.slice(0, 10).map((order, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium">
                        {order.transaction_type} {order.tradingsymbol}
                      </div>
                      <div className="text-sm text-gray-600">
                        {order.quantity} @ ₹{order.price?.toFixed(2)} ({order.order_type})
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${
                        order.status === 'COMPLETE' ? 'text-green-600' :
                        order.status === 'REJECTED' ? 'text-red-600' : 'text-yellow-600'
                      }`}>
                        {order.status}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(order.order_timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-gray-500 text-center py-4">
                  No orders yet. Place your first demo order!
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Demo Features */}
      <div className="mt-8">
        <DemoModeFeatures />
      </div>
    </div>
  );
}
