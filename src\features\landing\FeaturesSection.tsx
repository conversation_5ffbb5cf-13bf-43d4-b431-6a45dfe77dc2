'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Bot,
  Zap,
  Shield,
  Users,
  TrendingUp,
  Clock,
  Target,
  BarChart3,
  Lock,
  Smartphone
} from 'lucide-react';
import { motion } from 'framer-motion';
import { GlowingBorder } from '@/components/ui/animated-border';

const features = [
  {
    icon: Bot,
    title: "AI-Powered Trading",
    description: "Advanced algorithms analyze market patterns and execute trades with precision, learning from every transaction.",
    badge: "New"
  },
  {
    icon: Zap,
    title: "Real-time Execution",
    description: "Lightning-fast trade copying ensures your child accounts mirror master trades within milliseconds.",
    badge: null
  },
  {
    icon: Shield,
    title: "Bank-level Security",
    description: "Your trading data is protected with enterprise-grade encryption and security protocols.",
    badge: null
  },
  {
    icon: Users,
    title: "Master-Child Network",
    description: "Build and manage your trading network with unlimited child accounts and sophisticated controls.",
    badge: null
  },
  {
    icon: TrendingUp,
    title: "Performance Analytics",
    description: "Comprehensive dashboards track performance, profits, and trading patterns across all accounts.",
    badge: null
  },
  {
    icon: Clock,
    title: "24/7 Monitoring",
    description: "Continuous market monitoring ensures no trading opportunity is missed, even when you're away.",
    badge: null
  },
  {
    icon: Target,
    title: "Risk Management",
    description: "Built-in risk controls and position sizing help protect your capital and optimize returns.",
    badge: null
  },
  {
    icon: BarChart3,
    title: "Advanced Reporting",
    description: "Detailed reports and analytics help you understand and improve your trading strategies.",
    badge: null
  },
  {
    icon: Lock,
    title: "Secure API Integration",
    description: "Seamless integration with Zerodha and other brokers through secure, encrypted connections.",
    badge: null
  },
  {
    icon: Smartphone,
    title: "Mobile Ready",
    description: "Manage your trades and monitor performance on the go with our responsive mobile interface.",
    badge: null
  }
];

export default function FeaturesSection() {
  return (
    <section className="py-24 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Badge variant="outline" className="mb-4 border-2 border-transparent bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-cyan-500/10 hover:from-blue-500/20 hover:via-purple-500/20 hover:to-cyan-500/20 transition-all duration-300">
              Features
            </Badge>
          </motion.div>
          <motion.h2
            className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl lg:text-5xl"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Everything you need to{' '}
            <motion.span
              className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
              style={{
                backgroundSize: "200% 200%",
              }}
            >
              scale your trading
            </motion.span>
          </motion.h2>
          <motion.p
            className="mt-4 max-w-2xl mx-auto text-xl text-muted-foreground"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            Our platform provides all the tools and features you need to build a successful trading network.
          </motion.p>
        </motion.div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <GlowingBorder
                className="h-full"
                glowColor={index % 4 === 0 ? "blue" : index % 4 === 1 ? "purple" : index % 4 === 2 ? "cyan" : "pink"}
              >
                <Card className="relative group h-full border-0 bg-background/50 backdrop-blur-sm">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <motion.div
                          className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg group-hover:from-blue-500/20 group-hover:to-purple-500/20 transition-all duration-300"
                          whileHover={{ scale: 1.1, rotate: 5 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <motion.div
                            animate={{
                              rotate: feature.title === "AI-Powered Trading" ? [0, 360] : 0,
                              scale: feature.title === "Real-time Execution" ? [1, 1.1, 1] : 1
                            }}
                            transition={{
                              duration: feature.title === "AI-Powered Trading" ? 4 : 2,
                              repeat: Infinity,
                              ease: "linear"
                            }}
                          >
                            <feature.icon className="w-6 h-6 text-blue-600" />
                          </motion.div>
                        </motion.div>
                        {feature.badge && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.5 + index * 0.1, type: "spring" }}
                          >
                            <Badge variant="secondary" className="text-xs bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-300/50">
                              {feature.badge}
                            </Badge>
                          </motion.div>
                        )}
                      </div>
                    </div>
                    <CardTitle className="text-xl font-semibold text-foreground">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <p className="text-lg text-muted-foreground mb-6">
            Ready to experience the future of trading?
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <Badge variant="outline" className="px-4 py-2 border-2 border-transparent bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-cyan-500/10 hover:from-blue-500/20 hover:via-purple-500/20 hover:to-cyan-500/20 transition-all duration-300">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                >
                  <TrendingUp className="w-4 h-4 mr-2" />
                </motion.div>
                Start with our free plan
              </Badge>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <Badge variant="outline" className="px-4 py-2 border-2 border-transparent bg-gradient-to-r from-purple-500/10 via-cyan-500/10 to-blue-500/10 hover:from-purple-500/20 hover:via-cyan-500/20 hover:to-blue-500/20 transition-all duration-300">
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                >
                  <Shield className="w-4 h-4 mr-2" />
                </motion.div>
                No credit card required
              </Badge>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
