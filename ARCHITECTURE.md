# CopyTrade Application Architecture

## Overview

This document outlines the restructured architecture of the CopyTrade application, designed for scalability, maintainability, and enterprise-level development practices.

## Project Structure

```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Auth route group
│   ├── (dashboard)/              # Dashboard route group  
│   ├── (demo)/                   # Demo mode route group
│   ├── api/                      # API routes
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Home page
├── components/                   # Reusable UI components
│   ├── ui/                       # Basic UI components (buttons, inputs, etc.)
│   ├── layout/                   # Layout components (header, footer, etc.)
│   ├── forms/                    # Form components
│   └── common/                   # Common components
├── features/                     # Feature-based modules
│   ├── auth/                     # Authentication feature
│   ├── trading/                  # Trading feature
│   ├── dashboard/                # Dashboard feature
│   ├── user-management/          # User management feature
│   ├── zerodha-integration/      # Zerodha integration feature
│   ├── demo-mode/                # Demo mode feature
│   └── landing/                  # Landing page feature
├── lib/                         # Core utilities and configurations
│   ├── prisma.ts                # Prisma client
│   ├── supabase.ts              # Supabase client
│   └── utils/                   # Utility functions
├── types/                       # TypeScript type definitions
│   ├── auth.types.ts            # Authentication types
│   ├── trading.types.ts         # Trading types
│   ├── user.types.ts            # User types
│   ├── api.types.ts             # API types
│   ├── database.types.ts        # Database types
│   ├── zerodha.types.ts         # Zerodha API types
│   └── index.ts                 # Type exports
├── constants/                   # Application constants
│   ├── app.constants.ts         # App configuration
│   ├── api.constants.ts         # API endpoints and status codes
│   ├── trading.constants.ts     # Trading constants
│   ├── routes.constants.ts      # Route definitions
│   └── index.ts                 # Constants exports
├── hooks/                       # Custom React hooks
│   ├── use-auth.ts              # Authentication hook
│   ├── use-demo-mode.ts         # Demo mode hook
│   ├── use-local-storage.ts     # Local storage hook
│   ├── use-trading.ts           # Trading hook
│   ├── use-zerodha.ts           # Zerodha integration hook
│   └── index.ts                 # Hooks exports
├── providers/                   # Context providers
│   ├── AuthContext.tsx          # Authentication context
│   ├── theme-provider.tsx       # Theme provider
│   ├── theme-toggle.tsx         # Theme toggle component
│   └── index.ts                 # Providers exports
├── services/                    # Business logic services
│   ├── auditService.ts          # Audit logging service
│   ├── invitationService.ts     # Invitation service
│   ├── notificationService.ts   # Notification service
│   ├── portfolioService.ts      # Portfolio service
│   ├── relationshipService.ts   # User relationship service
│   ├── tradingService.ts        # Trading service
│   └── userService.ts           # User service
└── utils/                       # Pure utility functions
    ├── authService.ts           # Auth utilities
    ├── enhancedTradingService.ts # Enhanced trading utilities
    ├── mockTradingService.ts    # Mock trading utilities
    ├── orderUtils.ts            # Order utilities
    ├── tokenManager.ts          # Token management
    ├── zerodha.ts               # Zerodha utilities
    ├── zerodhaApi.ts            # Zerodha API utilities
    ├── format.ts                # Formatting utilities
    ├── validation.ts            # Validation utilities
    ├── api.ts                   # API utilities
    └── index.ts                 # Utils exports
```

## Key Architectural Decisions

### 1. Feature-Based Organization
- Each major feature has its own directory under `src/features/`
- Features contain components, hooks, and utilities specific to that feature
- Promotes modularity and easier maintenance

### 2. Separation of Concerns
- **Components**: Pure UI components with minimal business logic
- **Services**: Business logic and data manipulation
- **Utils**: Pure functions with no side effects
- **Hooks**: React-specific logic and state management
- **Types**: Centralized type definitions

### 3. Import Path Aliases
- `@/components/*` - UI components
- `@/features/*` - Feature modules
- `@/lib/*` - Core utilities
- `@/types/*` - Type definitions
- `@/constants/*` - Application constants
- `@/hooks/*` - Custom hooks
- `@/providers/*` - Context providers
- `@/services/*` - Business services
- `@/utils/*` - Utility functions

### 4. Type Safety
- Comprehensive TypeScript types for all data structures
- Strict type checking enabled
- API response types for better error handling

### 5. Scalable Patterns
- Consistent naming conventions
- Modular architecture
- Clear dependency management
- Separation of business logic from UI

## Naming Conventions

### Files and Directories
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuth.ts`)
- **Services**: camelCase with `Service` suffix (e.g., `tradingService.ts`)
- **Types**: camelCase with `.types.ts` suffix (e.g., `auth.types.ts`)
- **Constants**: camelCase with `.constants.ts` suffix (e.g., `api.constants.ts`)
- **Utils**: camelCase (e.g., `formatCurrency.ts`)
- **Directories**: kebab-case (e.g., `user-management/`)

### Variables and Functions
- **Variables**: camelCase (e.g., `userName`)
- **Functions**: camelCase (e.g., `getUserProfile`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS`)
- **Types/Interfaces**: PascalCase (e.g., `UserProfile`)

## Development Guidelines

### 1. Component Development
- Use functional components with hooks
- Implement proper TypeScript types
- Follow single responsibility principle
- Use composition over inheritance

### 2. State Management
- Use React Context for global state
- Custom hooks for feature-specific state
- Local state for component-specific data

### 3. API Integration
- Centralized API client with error handling
- Type-safe API responses
- Consistent error handling patterns

### 4. Testing Strategy
- Unit tests for utilities and services
- Integration tests for API endpoints
- Component tests for UI components
- E2E tests for critical user flows

### 5. Performance Considerations
- Code splitting by features
- Lazy loading for non-critical components
- Optimized bundle sizes
- Efficient re-rendering patterns

## Migration Benefits

1. **Improved Maintainability**: Clear structure makes it easier to locate and modify code
2. **Better Scalability**: Feature-based organization supports team growth
3. **Enhanced Developer Experience**: Better IDE support with path aliases
4. **Type Safety**: Comprehensive TypeScript coverage reduces runtime errors
5. **Consistent Patterns**: Standardized approaches across the codebase
6. **Easier Testing**: Modular structure facilitates unit and integration testing
7. **Better Performance**: Optimized imports and code splitting opportunities

## Next Steps

1. Update all import statements to use new path aliases
2. Migrate remaining components to feature directories
3. Implement comprehensive testing suite
4. Add proper error boundaries
5. Optimize bundle splitting
6. Add performance monitoring
7. Implement proper logging and monitoring
