import { useState, useEffect } from 'react';
import { useAuth } from './use-auth';
import { ZERODHA_CONFIG, LOCAL_STORAGE_KEYS } from '@/constants';
import type { ZerodhaAuthData, ZerodhaProfile } from '@/types';

export const useZerodha = () => {
  const { user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [profile, setProfile] = useState<ZerodhaProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check connection status on mount
  useEffect(() => {
    checkConnectionStatus();
  }, [user]);

  const checkConnectionStatus = () => {
    if (typeof window === 'undefined') return;
    
    try {
      const tokens = localStorage.getItem(LOCAL_STORAGE_KEYS.zerodhaTokens);
      const hasTokens = tokens && JSON.parse(tokens);
      setIsConnected(!!hasTokens && !!user?.isZerodhaConnected);
    } catch (error) {
      console.error('Error checking Zerodha connection:', error);
      setIsConnected(false);
    }
  };

  const connect = () => {
    if (!ZERODHA_CONFIG.apiKey) {
      setError('Zerodha API key not configured');
      return;
    }

    const loginUrl = `${ZERODHA_CONFIG.loginUrl}?api_key=${ZERODHA_CONFIG.apiKey}&v=3`;
    window.location.href = loginUrl;
  };

  const disconnect = () => {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(LOCAL_STORAGE_KEYS.zerodhaTokens);
      setIsConnected(false);
      setProfile(null);
      setError(null);
    } catch (error) {
      console.error('Error disconnecting from Zerodha:', error);
    }
  };

  const getProfile = async (): Promise<ZerodhaProfile | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/zerodha/profile');
      if (!response.ok) {
        throw new Error('Failed to fetch Zerodha profile');
      }
      
      const data = await response.json();
      setProfile(data);
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get profile';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const storeTokens = (authData: ZerodhaAuthData) => {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(LOCAL_STORAGE_KEYS.zerodhaTokens, JSON.stringify(authData));
      setIsConnected(true);
      setError(null);
    } catch (error) {
      console.error('Error storing Zerodha tokens:', error);
      setError('Failed to store authentication tokens');
    }
  };

  const getStoredTokens = (): ZerodhaAuthData | null => {
    if (typeof window === 'undefined') return null;
    
    try {
      const tokens = localStorage.getItem(LOCAL_STORAGE_KEYS.zerodhaTokens);
      return tokens ? JSON.parse(tokens) : null;
    } catch (error) {
      console.error('Error getting stored tokens:', error);
      return null;
    }
  };

  return {
    isConnected,
    profile,
    loading,
    error,
    connect,
    disconnect,
    getProfile,
    storeTokens,
    getStoredTokens,
    checkConnectionStatus,
  };
};
