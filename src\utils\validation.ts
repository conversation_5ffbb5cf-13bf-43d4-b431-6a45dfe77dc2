/**
 * Email validation
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Password validation
 */
export const isValidPassword = (password: string): boolean => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

/**
 * Phone number validation (Indian format)
 */
export const isValidPhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
};

/**
 * Trading symbol validation
 */
export const isValidTradingSymbol = (symbol: string): boolean => {
  // Basic validation for Indian stock symbols
  const symbolRegex = /^[A-Z0-9&-]{1,20}$/;
  return symbolRegex.test(symbol);
};

/**
 * Quantity validation
 */
export const isValidQuantity = (quantity: number): boolean => {
  return Number.isInteger(quantity) && quantity > 0;
};

/**
 * Price validation
 */
export const isValidPrice = (price: number): boolean => {
  return price > 0 && Number.isFinite(price);
};

/**
 * Zerodha User ID validation
 */
export const isValidZerodhaUserId = (userId: string): boolean => {
  // Zerodha user IDs are typically 6 characters alphanumeric
  const userIdRegex = /^[A-Z0-9]{6}$/;
  return userIdRegex.test(userId);
};

/**
 * API Key validation
 */
export const isValidApiKey = (apiKey: string): boolean => {
  // Basic validation for API keys (32 character alphanumeric)
  const apiKeyRegex = /^[a-z0-9]{32}$/;
  return apiKeyRegex.test(apiKey);
};

/**
 * URL validation
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Required field validation
 */
export const isRequired = (value: any): boolean => {
  if (typeof value === 'string') {
    return value.trim().length > 0;
  }
  return value !== null && value !== undefined;
};

/**
 * Minimum length validation
 */
export const hasMinLength = (value: string, minLength: number): boolean => {
  return value.length >= minLength;
};

/**
 * Maximum length validation
 */
export const hasMaxLength = (value: string, maxLength: number): boolean => {
  return value.length <= maxLength;
};

/**
 * Numeric range validation
 */
export const isInRange = (value: number, min: number, max: number): boolean => {
  return value >= min && value <= max;
};

/**
 * Form validation helper
 */
export interface ValidationRule {
  validator: (value: any) => boolean;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateField = (value: any, rules: ValidationRule[]): ValidationResult => {
  const errors: string[] = [];
  
  for (const rule of rules) {
    if (!rule.validator(value)) {
      errors.push(rule.message);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Common validation rules
 */
export const ValidationRules = {
  required: (message = 'This field is required'): ValidationRule => ({
    validator: isRequired,
    message,
  }),
  
  email: (message = 'Please enter a valid email address'): ValidationRule => ({
    validator: isValidEmail,
    message,
  }),
  
  password: (message = 'Password must be at least 8 characters with uppercase, lowercase, and number'): ValidationRule => ({
    validator: isValidPassword,
    message,
  }),
  
  minLength: (length: number, message?: string): ValidationRule => ({
    validator: (value: string) => hasMinLength(value, length),
    message: message || `Must be at least ${length} characters`,
  }),
  
  maxLength: (length: number, message?: string): ValidationRule => ({
    validator: (value: string) => hasMaxLength(value, length),
    message: message || `Must be no more than ${length} characters`,
  }),
  
  positiveNumber: (message = 'Must be a positive number'): ValidationRule => ({
    validator: (value: number) => value > 0,
    message,
  }),
};
