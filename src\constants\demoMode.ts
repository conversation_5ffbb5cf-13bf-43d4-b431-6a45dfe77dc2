// Demo Mode Configuration
// This allows the app to work without real Zerodha API while waiting for paid version

export const DEMO_MODE = {
  // Enable demo mode (set to false when you get paid Zerodha API)
  enabled: true,
  
  // Demo mode settings
  settings: {
    // Simulate API delays for realistic experience
    simulateApiDelay: true,
    
    // Show demo mode indicators in UI
    showDemoIndicators: true,
    
    // Allow mock authentication for child users
    allowMockAuth: true,
    
    // Auto-generate demo data
    generateDemoData: true,
    
    // Market simulation settings
    marketSimulation: {
      priceUpdateInterval: 30000, // 30 seconds
      maxPriceChange: 0.02, // ±2% per update
      tradingHours: {
        start: 9, // 9 AM
        end: 15.5 // 3:30 PM
      }
    }
  },
  
  // Demo user credentials for testing
  demoUsers: {
    master: {
      email: '<EMAIL>',
      password: 'demo123',
      name: 'Demo Master'
    },
    children: [
      {
        email: '<EMAIL>',
        password: 'demo123',
        name: 'Demo Child 1'
      },
      {
        email: '<EMAIL>',
        password: 'demo123',
        name: 'Demo Child 2'
      }
    ]
  },
  
  // Sample trades for demo
  sampleTrades: [
    {
      symbol: 'RELIANCE',
      exchange: 'NSE',
      transactionType: 'BUY' as const,
      quantity: 10,
      price: 2450.50,
      orderType: 'MARKET' as const,
      product: 'CNC' as const
    },
    {
      symbol: 'TCS',
      exchange: 'NSE',
      transactionType: 'BUY' as const,
      quantity: 5,
      price: 3650.75,
      orderType: 'LIMIT' as const,
      product: 'CNC' as const
    },
    {
      symbol: 'INFY',
      exchange: 'NSE',
      transactionType: 'SELL' as const,
      quantity: 8,
      price: 1580.25,
      orderType: 'MARKET' as const,
      product: 'CNC' as const
    }
  ]
};

// Helper functions for demo mode
export const isDemoMode = () => DEMO_MODE.enabled;

export const getDemoModeMessage = () => {
  if (!DEMO_MODE.enabled) return null;
  
  return {
    title: '🚀 Demo Mode Active',
    message: 'You are currently using CopyTrade in demo mode. All trades are simulated and no real money is involved.',
    type: 'info' as const
  };
};

export const shouldShowDemoIndicators = () => 
  DEMO_MODE.enabled && DEMO_MODE.settings.showDemoIndicators;

export const shouldSimulateApiDelay = () => 
  DEMO_MODE.enabled && DEMO_MODE.settings.simulateApiDelay;

export const canUseMockAuth = () => 
  DEMO_MODE.enabled && DEMO_MODE.settings.allowMockAuth;

// Mock authentication for demo mode
export const mockZerodhaAuth = async (email: string): Promise<{
  access_token: string;
  refresh_token: string;
  user_id: string;
  user_name: string;
  email: string;
}> => {
  if (!canUseMockAuth()) {
    throw new Error('Mock authentication not allowed');
  }

  // Simulate API delay
  if (shouldSimulateApiDelay()) {
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  }

  return {
    access_token: `mock_access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    refresh_token: `mock_refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    user_id: `MOCK_${email.replace('@', '_').replace('.', '_')}`,
    user_name: email.split('@')[0].replace('.', ' ').toUpperCase(),
    email: email
  };
};

// Generate demo portfolio data
export const generateDemoPortfolio = (userId: string) => {
  if (!DEMO_MODE.settings.generateDemoData) return null;

  const holdings = [
    { symbol: 'RELIANCE', quantity: 10, averagePrice: 2400, currentPrice: 2450.50 },
    { symbol: 'TCS', quantity: 5, averagePrice: 3600, currentPrice: 3650.75 },
    { symbol: 'INFY', quantity: 15, averagePrice: 1550, currentPrice: 1580.25 },
    { symbol: 'HDFCBANK', quantity: 8, averagePrice: 1620, currentPrice: 1650.80 }
  ].map(holding => ({
    ...holding,
    pnl: (holding.currentPrice - holding.averagePrice) * holding.quantity
  }));

  const totalValue = holdings.reduce((sum, h) => sum + (h.currentPrice * h.quantity), 0);
  const totalPnl = holdings.reduce((sum, h) => sum + h.pnl, 0);

  return {
    userId,
    holdings,
    totalValue,
    totalPnl
  };
};

// Check if we're in trading hours (for market simulation)
export const isMarketOpen = (): boolean => {
  const now = new Date();
  const hour = now.getHours() + (now.getMinutes() / 60);
  const { start, end } = DEMO_MODE.settings.marketSimulation.tradingHours;
  
  // Check if it's a weekday (Monday = 1, Sunday = 0)
  const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
  
  return isWeekday && hour >= start && hour <= end;
};

export default DEMO_MODE;
